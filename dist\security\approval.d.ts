import { EventEmitter } from 'events';
import type { ApprovalMode, ToolCall, RiskAssessment } from '../types';
export interface ApprovalRequest {
    toolCall: ToolCall;
    riskAssessment: RiskAssessment;
    timestamp: Date;
}
export declare class ApprovalSystem extends EventEmitter {
    private mode;
    private autoApprovedCommands;
    private blockedCommands;
    constructor(mode?: ApprovalMode);
    private initializeDefaultRules;
    setMode(mode: ApprovalMode): void;
    getMode(): ApprovalMode;
    requestApproval(toolCall: ToolCall, riskAssessment: RiskAssessment): Promise<boolean>;
    private isValidToolCall;
    private shouldAutoApprove;
    private promptUserApproval;
    private rememberApproval;
    private isCommandBlocked;
    private isCommandAutoApproved;
    private getCommandKey;
    private getCommandString;
    private formatRiskLevel;
    addAutoApprovedCommand(command: string): void;
    removeAutoApprovedCommand(command: string): void;
    addBlockedCommand(command: string): void;
    removeBlockedCommand(command: string): void;
    getAutoApprovedCommands(): string[];
    getBlockedCommands(): string[];
    clearSessionApprovals(): void;
    getApprovalStats(): {
        mode: ApprovalMode;
        autoApprovedCount: number;
        blockedCount: number;
    };
}
//# sourceMappingURL=approval.d.ts.map