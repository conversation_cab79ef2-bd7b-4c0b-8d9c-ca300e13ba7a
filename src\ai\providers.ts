import OpenAI from 'openai';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import { RetryManager } from '@/utils/retry';
import type {
  AgentConfig,
  ConversationMessage,
  ToolCall,
  ToolResult,
  SessionContext,
  StreamChunk,
  AIProvider,
} from '@/types';

export interface AIProviderConfig {
  provider: AIProvider;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface CompletionOptions {
  messages: ConversationMessage[];
  tools: any[];
  context: SessionContext;
  onChunk?: (chunk: StreamChunk) => void;
  onToolCall?: (toolCall: ToolCall) => Promise<ToolResult>;
}

export class AIProviderManager {
  private client: OpenAI;
  private config: AIProviderConfig;

  constructor(config: AgentConfig) {
    this.config = {
      provider: config.provider,
      model: config.model,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      maxTokens: config.maxTokens || 4096,
      temperature: config.temperature || 0.7,
    };

    this.client = this.createClient();
  }

  private createClient(): OpenAI {
    const clientConfig: any = {
      apiKey: this.config.apiKey || process.env['OPENAI_API_KEY'],
    };

    // Configure for different providers
    switch (this.config.provider) {
      case 'openai':
        // Default OpenAI configuration
        break;

      case 'deepseek':
        clientConfig.baseURL = this.config.baseUrl || 'https://api.deepseek.com/v1';
        clientConfig.apiKey = this.config.apiKey || process.env['DEEPSEEK_API_KEY'];
        break;

      case 'ollama':
        clientConfig.baseURL = this.config.baseUrl || 'http://localhost:11434/v1';
        clientConfig.apiKey = 'ollama'; // Ollama doesn't require a real API key
        break;

      case 'azure':
        clientConfig.baseURL = this.config.baseUrl;
        clientConfig.apiKey = this.config.apiKey || process.env['AZURE_OPENAI_API_KEY'];
        clientConfig.defaultHeaders = {
          'api-key': clientConfig.apiKey,
        };
        break;

      default:
        throw new Error(`Unsupported AI provider: ${this.config.provider}`);
    }

    return new OpenAI(clientConfig);
  }

  async updateConfig(newConfig: AgentConfig): Promise<void> {
    this.config = {
      provider: newConfig.provider,
      model: newConfig.model,
      apiKey: newConfig.apiKey,
      baseUrl: newConfig.baseUrl,
      maxTokens: newConfig.maxTokens || this.config.maxTokens,
      temperature: newConfig.temperature || this.config.temperature,
    };

    this.client = this.createClient();
    logger.info('AI provider config updated', { 
      provider: this.config.provider, 
      model: this.config.model 
    });
  }

  async streamCompletion(options: CompletionOptions): Promise<ConversationMessage> {
    const { messages, tools, context, onChunk, onToolCall } = options;

    // Prepare system message with context
    const systemMessage = this.buildSystemMessage(context);

    // Convert messages to OpenAI format
    const openaiMessages = this.convertMessages([systemMessage, ...messages]);

    // Prepare tools for OpenAI format
    const openaiTools = this.convertTools(tools);

    const result = await RetryManager.retry(
      async () => {
        const stream = await this.client.chat.completions.create({
          model: this.config.model,
          messages: openaiMessages,
          tools: openaiTools.length > 0 ? openaiTools : undefined,
          tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
          max_tokens: this.config.maxTokens || null,
          temperature: this.config.temperature || null,
          stream: true,
        });

        return stream;
      },
      {
        maxAttempts: 3,
        baseDelay: 1000,
        exponentialBackoff: true,
        retryCondition: (error) => RetryManager.isRetryableError(error),
        onRetry: (error, attempt) => {
          logger.warn('Retrying AI completion', {
            attempt,
            error: error.message,
            provider: this.config.provider
          });

          onChunk?.({
            type: 'error',
            content: `Retrying request (attempt ${attempt})...`,
            metadata: { error: error.message },
          });
        },
      }
    );

    if (!result.success || !result.result) {
      throw result.error || new Error('Failed to get AI response');
    }

    const stream = result.result;

    try {

      let assistantMessage = '';
      const toolCalls: ToolCall[] = [];
      let currentToolCall: Partial<ToolCall> | null = null;
      let argumentsBuffer = '';

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          assistantMessage += delta.content;
          onChunk?.({
            type: 'text',
            content: delta.content,
          });
        }

        if (delta?.tool_calls) {
          for (const toolCallDelta of delta.tool_calls) {
            const index = toolCallDelta.index;

            // Process tool call delta

            // Handle tool call by index - each index represents a different tool call
            if (index !== undefined) {
              // If this is a new tool call (different index or first time)
              if (!currentToolCall || index !== (currentToolCall as any).index) {
                // Finish previous tool call if it exists and is complete
                if (currentToolCall && this.isValidToolCall(currentToolCall)) {
                  logger.debug('Finishing previous tool call', {
                    toolName: currentToolCall.name,
                    arguments: currentToolCall.arguments
                  });
                  toolCalls.push(currentToolCall as ToolCall);
                }

                // Start new tool call
                currentToolCall = {
                  id: toolCallDelta.id || nanoid(),
                  name: toolCallDelta.function?.name || '',
                  arguments: {},
                  riskLevel: 'safe',
                } as any;
                (currentToolCall as any).index = index;
                argumentsBuffer = '';

                // Tool call started
              }

              // Update tool call name if provided
              if (toolCallDelta.function?.name && currentToolCall) {
                currentToolCall.name = toolCallDelta.function.name;
              }

              // Accumulate arguments if provided
              if (toolCallDelta.function?.arguments && currentToolCall) {
                argumentsBuffer += toolCallDelta.function.arguments;

                try {
                  // Try to parse the accumulated arguments
                  const args = JSON.parse(argumentsBuffer);
                  currentToolCall.arguments = args;
                } catch (error) {
                  // Partial JSON, continue accumulating
                  // This is normal for streaming responses
                }
              }
            }
          }
        }

        if (chunk.choices[0]?.finish_reason === 'tool_calls') {
          // Finish the last tool call if it's valid
          if (currentToolCall && this.isValidToolCall(currentToolCall)) {
            toolCalls.push(currentToolCall as ToolCall);
          }
          currentToolCall = null;
          argumentsBuffer = '';
        }
      }

      // Handle any remaining tool call that wasn't finished by finish_reason
      if (currentToolCall && this.isValidToolCall(currentToolCall)) {
        toolCalls.push(currentToolCall as ToolCall);
      }

      // Create the assistant message with tool calls
      const assistantMessageWithTools: ConversationMessage = {
        id: nanoid(),
        role: 'assistant',
        content: assistantMessage || '',
        timestamp: new Date(),
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      };

      // Execute tool calls if any and return the assistant message
      // The tool results will be handled separately in the conversation flow
      if (toolCalls.length > 0 && onToolCall) {
        const toolResults: ToolResult[] = [];

        for (const toolCall of toolCalls) {
          onChunk?.({
            type: 'tool_call',
            content: `Executing ${toolCall.name}...`,
            metadata: { toolCall },
          });

          const result = await onToolCall(toolCall);
          toolResults.push(result);

          onChunk?.({
            type: 'tool_result',
            content: result.success ? result.output : `Error: ${result.error}`,
            metadata: { result },
          });
        }

        // Add tool results to the assistant message for reference
        assistantMessageWithTools.toolResults = toolResults;
      }

      return assistantMessageWithTools;

    } catch (error) {
      logger.error('Error in AI completion', error);
      throw error;
    }
  }



  private buildSystemMessage(context: SessionContext): ConversationMessage {
    const { projectStructure, environmentInfo } = context;
    
    const systemPrompt = `You are Kritrima, an advanced AI-powered CLI assistant with agentic capabilities. You can execute shell commands, manipulate files, and help with development tasks.

CURRENT CONTEXT:
- Working Directory: ${projectStructure.root}
- Project Type: ${projectStructure.type}
- Language: ${projectStructure.language || 'Unknown'}
- Framework: ${projectStructure.framework || 'None'}
- Package Manager: ${projectStructure.packageManager || 'None'}
- Platform: ${environmentInfo.platform}
- Node Version: ${environmentInfo.nodeVersion}

CAPABILITIES:
- Execute shell commands safely
- Read, write, create, delete, move, and copy files
- Search files with grep and glob patterns
- Analyze project structure and dependencies
- Install packages and manage dependencies
- Run tests and build processes
- Git operations
- System information gathering

SAFETY GUIDELINES:
- Always assess risk before executing commands
- Ask for confirmation for destructive operations
- Prefer safe, reversible operations
- Provide clear explanations of what you're doing
- Use appropriate tools for each task

TOOL USAGE INSTRUCTIONS:
When a user asks to:
- "show me [filename]" or "read [filename]" or "show me the [filename] file" → ALWAYS use file_read with {"path": "filename"}
- "list files" or "show directory" → use list_directory with {} or {"path": "."}
- "run [command]" or "execute [command]" → use shell with {"command": "command"}
- "create [filename]" → use file_create with {"path": "filename", "content": "..."}
- "delete [filename]" → use file_delete with {"path": "filename"}
- "search for [pattern]" → use file_search with {"pattern": "pattern"}

AVAILABLE TOOLS:
- shell: Execute shell commands (REQUIRED: 'command' parameter)
- file_read: Read file contents (REQUIRED: 'path' parameter)
- file_write: Write content to files (REQUIRED: 'path' and 'content' parameters)
- file_create: Create new files (REQUIRED: 'path' parameter)
- file_delete: Delete files (REQUIRED: 'path' parameter)
- file_move: Move/rename files (REQUIRED: 'source' and 'destination' parameters)
- file_copy: Copy files (REQUIRED: 'source' and 'destination' parameters)
- file_search: Search files with patterns (REQUIRED: 'pattern' parameter)
- grep: Search text in files (REQUIRED: 'pattern' and 'path' parameters)
- list_directory: List directory contents (OPTIONAL: 'path' parameter, defaults to current directory)
- system_info: Get system information (NO parameters required)

CRITICAL RULES FOR TOOL CALLS:
1. ALWAYS provide ALL required parameters when calling tools
2. NEVER call a tool with empty arguments {} if it has required parameters
3. ALWAYS include the exact parameter names as specified
4. For file operations, always provide the full or relative path

MANDATORY EXAMPLES:
- User: "show me package.json" → MUST call file_read with {"path": "package.json"}
- User: "show me the package.json file" → MUST call file_read with {"path": "package.json"}
- User: "read the README.md" → MUST call file_read with {"path": "README.md"}
- User: "list files" → Call list_directory with {} (no required parameters)
- User: "run npm install" → MUST call shell with {"command": "npm install"}

Be helpful, accurate, and safe in all operations.`;

    return {
      id: nanoid(),
      role: 'system',
      content: systemPrompt,
      timestamp: new Date(),
    };
  }

  private convertMessages(messages: ConversationMessage[]): any[] {
    return messages.map(msg => this.convertMessage(msg));
  }

  private convertMessage(message: ConversationMessage): any {
    const converted: any = {
      role: message.role,
      content: message.content || '',
    };

    // Handle assistant messages with tool calls
    if (message.role === 'assistant' && message.toolCalls) {
      converted.tool_calls = message.toolCalls.map(tc => ({
        id: tc.id,
        type: 'function',
        function: {
          name: tc.name,
          arguments: JSON.stringify(tc.arguments),
        },
      }));
    }

    // Handle tool messages - these must have tool_call_id
    if (message.role === 'tool' && message.toolCallId) {
      converted.tool_call_id = message.toolCallId;
      // Ensure content is not empty for tool messages
      if (!converted.content) {
        converted.content = 'Tool execution completed';
      }
    }

    return converted;
  }

  private convertTools(tools: any[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
      });

      return !!response.choices[0]?.message?.content;
    } catch (error) {
      logger.error('AI provider connection test failed', error);
      return false;
    }
  }

  private isValidToolCall(toolCall: Partial<ToolCall>): toolCall is ToolCall {
    const isValid = !!(
      toolCall.id &&
      toolCall.name &&
      toolCall.name.trim() !== '' &&
      toolCall.arguments !== undefined &&
      toolCall.riskLevel
    );

    if (!isValid) {
      logger.debug('AI Provider tool call validation failed', {
        hasId: !!toolCall.id,
        hasName: !!toolCall.name,
        nameNotEmpty: !!(toolCall.name && toolCall.name.trim() !== ''),
        hasArguments: toolCall.arguments !== undefined,
        hasRiskLevel: !!toolCall.riskLevel,
        toolCall
      });
    }

    return isValid;
  }

  getConfig(): AIProviderConfig {
    return { ...this.config };
  }
}
