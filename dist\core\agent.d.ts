import { EventEmitter } from 'events';
import type { AgentConfig, ConversationMessage, ToolCall, ToolResult, StreamHandler, EventHandler, Session } from '../types';
export declare class Agent extends EventEmitter {
    private config;
    private sessionManager;
    private contextManager;
    private aiProvider;
    private toolRegistry;
    private approvalSystem;
    private riskValidator;
    private currentSession;
    private isProcessing;
    constructor(config: AgentConfig);
    private setupEventHandlers;
    initialize(): Promise<void>;
    processMessage(message: string, streamHandler?: StreamHandler): Promise<ConversationMessage>;
    private handleToolCall;
    private isValidToolCall;
    getSession(): Promise<Session | null>;
    getSessions(): Promise<Session[]>;
    switchSession(sessionId: string): Promise<void>;
    createNewSession(): Promise<Session>;
    deleteSession(sessionId: string): Promise<void>;
    updateConfig(newConfig: Partial<AgentConfig>): Promise<void>;
    getConfig(): AgentConfig;
    getContext(): Promise<import("../types").SessionContext>;
    shutdown(): Promise<void>;
    onEvent<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): void;
    offEvent<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): void;
    isReady(): boolean;
    getStatus(): {
        ready: boolean;
        processing: boolean;
        sessionId: string | undefined;
        provider: import("../types").AIProvider;
        model: string;
        approvalMode: import("../types").ApprovalMode;
    };
    executeToolChain(toolCalls: ToolCall[], options?: {
        parallel?: boolean;
        continueOnError?: boolean;
    }): Promise<ToolResult[]>;
    executeToolsInParallel(toolCalls: ToolCall[]): Promise<ToolResult[]>;
    refreshContext(): Promise<void>;
    indexCurrentDirectory(): Promise<void>;
}
//# sourceMappingURL=agent.d.ts.map